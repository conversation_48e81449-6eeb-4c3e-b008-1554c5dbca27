import { useCallback, useRef, useState } from 'react';
import { MusicControlProps, NatureSoundPlayer } from './types';

// Type guard to check if playlist has nature sounds
function hasNatureSoundsPlaylist(playlist: MusicControlProps['playlist']): playlist is {
  id: string;
  name: string;
  natureSounds: {
    id: string;
    title: string;
    src?: string | null;
    category?: string[];
  }[];
  [key: string]: any;
} {
  return playlist !== null && 
         typeof playlist === 'object' && 
         'natureSounds' in playlist && 
         Array.isArray(playlist.natureSounds) && 
         playlist.natureSounds.length > 0;
}

export function useNatureSounds(playlist: MusicControlProps['playlist']) {
  const [natureSounds, setNatureSounds] = useState<NatureSoundPlayer[]>([]);
  const [isNatureSoundsExpanded, setIsNatureSoundsExpanded] = useState(true);

  // Track all audio elements to prevent duplicates
  const audioElementsMapRef = useRef(new Map<string, HTMLAudioElement>());

  // Toggle nature sound playback
  const toggleNatureSound = useCallback((id: string) => {
    // First, check if there are any other audio elements with this ID in the DOM
    // that might have been created outside our component
    const existingElements = document.querySelectorAll(`audio[data-nature-sound="${id}"]`);

    // Pause and remove any duplicate elements that aren't managed by our component
    existingElements.forEach(element => {
      const audioEl = element as HTMLAudioElement;
      // Check if this is not our managed audio element
      if (audioEl && !natureSounds.some(s => s.id === id && s.audioElement === audioEl)) {
        audioEl.pause();
        audioEl.src = '';
        if (audioEl.parentNode) {
          audioEl.parentNode.removeChild(audioEl);
        }
      }
    });

    setNatureSounds(prev =>
      prev.map(sound => {
        if (sound.id === id) {
          const newIsPlaying = !sound.isPlaying;

          if (sound.audioElement) {
            if (newIsPlaying) {
              // When turning on, create a new audio element if needed
              if (!sound.audioElement.src) {
                // Find the source from the playlist
                if (hasNatureSoundsPlaylist(playlist)) {
                  const sourceSound = playlist.natureSounds.find(s => s.id === id);
                  if (sourceSound && sourceSound.src) {
                    sound.audioElement.src = sourceSound.src;
                    sound.audioElement.loop = true;
                    sound.audioElement.dataset.natureSound = id; // Add data attribute
                  }
                }
              }

              // Set the volume first based on mute state
              sound.audioElement.volume = sound.isMuted ? 0 : sound.volume / 100;

              // Make sure we're using the audio element from our map
              if (audioElementsMapRef.current.has(id)) {
                const trackedAudio = audioElementsMapRef.current.get(id)!;
                if (trackedAudio !== sound.audioElement) {
                  // If we have a different audio element in our map, use that one
                  trackedAudio.volume = sound.isMuted ? 0 : sound.volume / 100;
                  trackedAudio.play()
                    .catch(() => {});

                  // Update our reference
                  sound.audioElement = trackedAudio;
                } else {
                  // Our references match, just play
                  sound.audioElement.play()
                    .catch(() => {});
                }
              } else {
                // No tracked audio, play and track this one
                sound.audioElement.play()
                  .catch(() => {});
                audioElementsMapRef.current.set(id, sound.audioElement);
              }
            } else {
              // When turning off, pause the audio
              sound.audioElement.pause();

              // Also pause any tracked audio element if it's different
              if (audioElementsMapRef.current.has(id)) {
                const trackedAudio = audioElementsMapRef.current.get(id)!;
                if (trackedAudio !== sound.audioElement) {
                  trackedAudio.pause();
                }
              }
            }
          }

          return { ...sound, isPlaying: newIsPlaying };
        }
        return sound;
      })
    );
  }, [natureSounds, playlist]);

  // Handle nature sound volume changes
  const handleNatureSoundVolume = useCallback((id: string, value: number[]) => {
    const newVolume = value[0];

    // Check for any duplicate audio elements with this ID
    const existingElements = document.querySelectorAll(`audio[data-nature-sound="${id}"]`);

    setNatureSounds(prev =>
      prev.map(sound => {
        if (sound.id === id) {
          // Only update the actual audio volume if:
          // 1. The audio element exists
          // 2. The sound is not muted
          // 3. The sound is currently playing
          if (sound.audioElement && !sound.isMuted && sound.isPlaying) {
            sound.audioElement.volume = newVolume / 100;
          }

          // Also apply volume to any tracked audio element if it's different
          if (audioElementsMapRef.current.has(id)) {
            const trackedAudio = audioElementsMapRef.current.get(id)!;
            if (!sound.audioElement || trackedAudio !== sound.audioElement) {
              if (!sound.isMuted && sound.isPlaying) {
                trackedAudio.volume = newVolume / 100;
              }
            }
          }

          // Also apply volume to any other audio elements with this ID that might exist
          existingElements.forEach(element => {
            const audioEl = element as HTMLAudioElement;
            // Check if this is not our managed audio element
            if (audioEl && (!sound.audioElement || audioEl !== sound.audioElement)) {
              if (!sound.isMuted && sound.isPlaying) {
                audioEl.volume = newVolume / 100;
              }
            }
          });

          // Always update the stored volume value
          return { ...sound, volume: newVolume };
        }
        return sound;
      })
    );
  }, []);

  // Toggle mute for nature sound
  const toggleNatureSoundMute = useCallback((id: string) => {
    // Check for any duplicate audio elements with this ID
    const existingElements = document.querySelectorAll(`audio[data-nature-sound="${id}"]`);

    setNatureSounds(prev =>
      prev.map(sound => {
        if (sound.id === id) {
          const newIsMuted = !sound.isMuted;

          // Store current volume before muting (from our managed audio element)
          let currentVolume = 50; // Default
          if (sound.audioElement) {
            currentVolume = sound.audioElement.volume * 100;
          }

          // Only update stored volume if it's not already 0
          const updatedVolume = sound.volume === 0 ? currentVolume : sound.volume;

          // Apply mute to our managed audio element
          if (sound.audioElement) {
            if (newIsMuted) {
              // Set volume to 0 (mute)
              sound.audioElement.volume = 0;
            } else if (sound.isPlaying) {
              // Unmute - restore volume only if the sound is playing
              sound.audioElement.volume = sound.volume / 100;
            }
          }

          // Also apply mute to any tracked audio element if it's different
          if (audioElementsMapRef.current.has(id)) {
            const trackedAudio = audioElementsMapRef.current.get(id)!;
            if (!sound.audioElement || trackedAudio !== sound.audioElement) {
              if (newIsMuted) {
                trackedAudio.volume = 0;
              } else if (sound.isPlaying) {
                trackedAudio.volume = sound.volume / 100;
              }
            }
          }

          // Also mute any other audio elements with this ID that might exist
          existingElements.forEach(element => {
            const audioEl = element as HTMLAudioElement;
            // Check if this is not our managed audio element
            if (audioEl && (!sound.audioElement || audioEl !== sound.audioElement)) {
              if (newIsMuted) {
                audioEl.volume = 0;
              } else if (sound.isPlaying) {
                audioEl.volume = sound.volume / 100;
              }
            }
          });

          return { ...sound, isMuted: newIsMuted, volume: updatedVolume };
        }
        return sound;
      })
    );
  }, []);

  // Initialize nature sounds
  const initializeNatureSounds = useCallback((playlist: MusicControlProps['playlist']) => {
    if (!hasNatureSoundsPlaylist(playlist)) {
      setNatureSounds([]);
      return;
    }

    // Create nature sound players
    const sounds = playlist.natureSounds.map(sound => {
      // Check if we already have an audio element for this sound in our global map
      let audioElement: HTMLAudioElement;

      if (audioElementsMapRef.current.has(sound.id)) {
        // Reuse existing audio element
        audioElement = audioElementsMapRef.current.get(sound.id)!;

        // Reset properties
        audioElement.pause();
        audioElement.currentTime = 0;
        audioElement.volume = 0; // Start at 0 for fade-in
        audioElement.loop = true;
      } else {
        // Create new audio element
        audioElement = new Audio(sound.src || '');
        audioElement.volume = 0; // Start at 0 for fade-in
        audioElement.loop = true;

        // Store in our global map
        audioElementsMapRef.current.set(sound.id, audioElement);
      }

      // Add a data attribute to identify this audio element
      audioElement.dataset.natureSound = sound.id;

      return {
        id: sound.id,
        title: sound.title,
        audioElement,
        isPlaying: true, // Start with playing state
        volume: 50,
        isMuted: false
      };
    });

    // Update state with the new sounds
    setNatureSounds(sounds);

    // Play them after a short delay and gradually increase volume
    setTimeout(() => {
      sounds.forEach(sound => {
        if (sound.audioElement && sound.isPlaying && !sound.isMuted) {
          sound.audioElement.play()
            .catch(() => {
              // Suppressed error handling
            });
          // Gradually increase volume from 0 to the target volume
          let volume = 0;
          const targetVolume = sound.volume / 100;
          const fadeInInterval = setInterval(() => {
            if (volume < targetVolume) {
              volume += 0.05; // Increase by 5% each step
              if (sound.audioElement) {
                sound.audioElement.volume = Math.min(volume, targetVolume);
              }
            } else {
              clearInterval(fadeInInterval);
            }
          }, 100); // Adjust every 100ms for a smooth fade-in
        }
      });
    }, 300);
  }, []);

  // Cleanup all nature sounds
  const cleanupNatureSounds = useCallback(() => {
    // Capture the current state and refs
    const currentNatureSounds = natureSounds;
    const currentAudioElementsMap = audioElementsMapRef.current;
    
    currentNatureSounds.forEach(sound => {
      if (sound.audioElement) {
        sound.audioElement.pause();
        sound.audioElement.onended = null;
        sound.audioElement.ontimeupdate = null;
        sound.audioElement.src = '';
        sound.audioElement = null;
      }
    });

    // Clear the audio elements map using the captured reference
    currentAudioElementsMap.forEach(audio => {
      audio.pause();
      audio.onended = null;
      audio.ontimeupdate = null;
      audio.src = '';
    });
    currentAudioElementsMap.clear();

    // Reset state
    setNatureSounds([]);
  }, [natureSounds]);

  return {
    natureSounds,
    setNatureSounds,
    isNatureSoundsExpanded,
    setIsNatureSoundsExpanded,
    audioElementsMapRef,
    toggleNatureSound,
    handleNatureSoundVolume,
    toggleNatureSoundMute,
    initializeNatureSounds,
    cleanupNatureSounds,
    playingNatureSoundsCount: natureSounds.filter(sound => sound.isPlaying).length
  };
} 